<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上级单位共享威胁情报库</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        body {
            display: flex;
            background-color: #f5f6fa;
            min-height: 100vh;
        }
        .sidebar {
            width: 200px;
            background-color: #fff;
            height: 100vh;
            border-right: 1px solid #e0e0e0;
            position: sticky;
            top: 0;
            overflow-y: auto;
            transition: all 0.3s ease;
        }
        .logo {
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
            text-align: center;
            font-size: 14px;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .logo-icon {
            margin-right: 8px;
            font-size: 18px;
        }
        .menu {
            list-style: none;
        }
        .menu-item {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            position: relative;
            transition: all 0.2s ease;
        }
        .menu-item.active {
            background-color: #f0f6ff;
            color: #4285f4;
            border-left: 3px solid #4285f4;
        }
        .menu-item:hover {
            background-color: #f9f9f9;
        }
        .menu-item i {
            margin-right: 8px;
            width: 20px;
            text-align: center;
        }
        .submenu {
            list-style: none;
            padding-left: 20px;
            display: none;
            background-color: #fafafa;
        }
        .menu-item.active .submenu {
            display: block;
        }
        .submenu-item {
            padding: 10px 15px;
            font-size: 13px;
            color: #666;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .submenu-item.active {
            color: #4285f4;
            font-weight: 500;
        }
        .submenu-item:hover {
            color: #4285f4;
            background-color: #f5f5f5;
        }
        .content {
            flex: 1;
            padding: 20px;
            overflow-x: auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .title {
            font-size: 24px;
            color: #333;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 4px;
            padding: 15px;
            flex: 1;
            min-width: 200px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .stat-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .stat-value.green {
            color: #34a853;
        }
        .stat-value.blue {
            color: #4285f4;
        }
        .stat-value.orange {
            color: #fbbc05;
        }
        .stat-value.red {
            color: #ea4335;
        }
        .search-bar {
            display: flex;
            margin-bottom: 20px;
            gap: 10px;
            flex-wrap: wrap;
        }
        .search-input {
            flex: 1;
            min-width: 200px;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }
        .search-input:focus {
            border-color: #4285f4;
            outline: none;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }
        .search-btn {
            padding: 10px 20px;
            background-color: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s ease;
        }
        .search-btn:hover {
            background-color: #3367d6;
        }
        .table-container {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }
        .table-responsive {
            overflow-x: auto;
            width: 100%;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }
        /* IOC列宽度控制 */
        table th:nth-child(3), table td:nth-child(3) {
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        th {
            background-color: #f9f9f9;
            color: #333;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
            cursor: pointer;
        }
        th:hover {
            background-color: #f0f0f0;
        }
        th::after {
            content: '⇅';
            margin-left: 5px;
            color: #aaa;
            font-size: 12px;
        }
        th.sort-asc::after {
            content: '↑';
            color: #4285f4;
        }
        th.sort-desc::after {
            content: '↓';
            color: #4285f4;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .pagination {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
            gap: 5px;
            flex-wrap: wrap;
        }
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background-color: #fff;
            cursor: pointer;
            font-size: 14px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        .page-btn.active {
            background-color: #4285f4;
            color: white;
            border-color: #4285f4;
        }
        .page-btn:hover:not(.active) {
            background-color: #f5f5f5;
        }
        .status {
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            text-align: center;
            min-width: 60px;
        }
        .status.green {
            background-color: #e6f4ea;
            color: #34a853;
        }
        .status.red {
            background-color: #fce8e6;
            color: #ea4335;
        }
        .status.orange {
            background-color: #fef7e0;
            color: #fbbc05;
        }
        .dropdown {
            position: relative;
            display: inline-block;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
            border-radius: 4px;
            overflow: hidden;
        }
        .dropdown:hover .dropdown-content {
            display: block;
        }
        .dropdown-item {
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            color: #333;
            font-size: 14px;
            transition: background-color 0.2s ease;
        }
        .dropdown-item:hover {
            background-color: #f1f1f1;
        }
        .filter-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        .filter-tag {
            background-color: #e8f0fe;
            color: #4285f4;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        .filter-tag .close {
            margin-left: 5px;
            font-weight: bold;
        }
        .filter-tag:hover {
            background-color: #d2e3fc;
        }
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #333;
        }
        .table-actions {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        .table-info {
            font-size: 14px;
            color: #666;
        }
        .export-btn {
            padding: 8px 15px;
            background-color: #34a853;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: background-color 0.2s ease;
        }
        .export-btn:hover {
            background-color: #2d9144;
        }
        .import-btn {
            padding: 8px 15px;
            background-color: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            margin-right: 10px;
            transition: background-color 0.2s ease;
        }
        .import-btn:hover {
            background-color: #3367d6;
        }
        .header-actions {
            display: flex;
            gap: 10px;
        }
        .file-input {
            display: none;
        }
        .tooltip {
            position: relative;
            display: inline-block;
            width: 100%;
            cursor: help;
        }
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 250px;
            background-color: #333;
            color: #fff;
            text-align: left;
            border-radius: 4px;
            padding: 8px;
            position: absolute;
            z-index: 100;
            bottom: 125%;
            left: 0;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            word-wrap: break-word;
            white-space: normal;
        }
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                flex-direction: column;
            }
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                display: none;
            }
            .sidebar.active {
                display: block;
            }
            .mobile-menu-toggle {
                display: block;
                margin-right: 10px;
            }
            .header {
                flex-direction: column;
                align-items: flex-start;
            }
            .stats {
                flex-direction: column;
            }
            .stat-card {
                width: 100%;
            }
            .search-bar {
                flex-direction: column;
            }
            .search-input {
                width: 100%;
            }
            .table-responsive {
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
    <button class="mobile-menu-toggle" onclick="toggleMenu()">☰</button>
    
    <div class="sidebar" id="sidebar">
        <div class="logo">
            <span class="logo-icon">🔒</span>
            <span>网络安全威胁情报系统</span>
        </div>
        <ul class="menu">
            <li class="menu-item">
                <i>📊</i> 情报总览
            </li>
            <li class="menu-item">
                <i>🔍</i> 情报查询
            </li>
            <li class="menu-item active">
                <i>📚</i> 情报库
                <ul class="submenu">
                    <li class="submenu-item">本地威胁情报库</li>
                    <li class="submenu-item active">上级单位共享威胁情报库</li>
                    <li class="submenu-item">第三方威胁情报库</li>
                </ul>
            </li>
            <li class="menu-item">
                <i>📝</i> 情报分析
            </li>
            <li class="menu-item">
                <i>🔔</i> 预警通知
            </li>
            <li class="menu-item">
                <i>⚙️</i> 系统设置
            </li>
        </ul>
    </div>
    
    <div class="content">
        <div class="header">
            <h1 class="title">上级单位共享威胁情报库</h1>
            <div class="header-actions">
                <input type="file" id="fileInput" class="file-input" accept=".csv,.xlsx,.json">
                <button class="import-btn" onclick="document.getElementById('fileInput').click()"><i>📤</i> 导入数据</button>
                <button class="export-btn"><i>📥</i> 导出数据</button>
            </div>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-title">情报总数</div>
                <div class="stat-value blue">1,276</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">本月新增</div>
                <div class="stat-value green">127</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">命中攻击总数</div>
                <div class="stat-value orange">364</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">命中率</div>
                <div class="stat-value red">28.5%</div>
            </div>
        </div>
        
        <div class="search-bar">
            <input type="text" class="search-input" id="searchInput" placeholder="搜索IOC、恶意类别、数据来源等..." onkeyup="searchTable()">
            <div class="dropdown">
                <button class="search-btn">高级筛选 ▼</button>
                <div class="dropdown-content">
                    <a href="#" class="dropdown-item" onclick="addFilter('IOC类型', 'IP')">按IOC类型筛选</a>
                    <a href="#" class="dropdown-item" onclick="addFilter('恶意类别', '勒索软件')">按恶意类别筛选</a>
                    <a href="#" class="dropdown-item" onclick="addFilter('数据来源', '国家网信办')">按数据来源筛选</a>
                    <a href="#" class="dropdown-item" onclick="addFilter('下发时间', '2023-04')">按时间范围筛选</a>
                </div>
            </div>
            <button class="search-btn" onclick="searchTable()">搜索</button>
        </div>
        
        <div class="filter-tags" id="filterTags">
            <!-- 筛选标签会动态添加到这里 -->
        </div>
        
        <div class="table-actions">
            <div class="table-info">显示 <span id="visibleRows">10</span> 条记录，共 <span id="totalRows">10</span> 条</div>
        </div>
        
        <div class="table-container">
            <div class="table-responsive">
                <table id="dataTable">
                    <thead>
                        <tr>
                            <th onclick="sortTable(0)">批次</th>
                            <th onclick="sortTable(1)">IOC类型</th>
                            <th onclick="sortTable(2)">IOC</th>
                            <th onclick="sortTable(3)">恶意类别</th>
                            <th onclick="sortTable(4)">下发时间</th>
                            <th onclick="sortTable(5)">处置建议</th>
                            <th onclick="sortTable(6)">数据来源</th>
                            <th onclick="sortTable(7)">情报共享单位</th>
                            <th onclick="sortTable(8)">情报利用单位</th>
                            <th onclick="sortTable(9)">是否命中攻击</th>
                            <th onclick="sortTable(10)">命中攻击次数</th>
                            <th onclick="sortTable(11)">占比</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2023-001</td>
                            <td>IP</td>
                            <td class="tooltip">*************<span class="tooltiptext">内网IP地址，可能是内部攻击源</span></td>
                            <td>勒索软件</td>
                            <td>2023-04-15</td>
                            <td>拦截</td>
                            <td>国家网信办</td>
                            <td>省网信办</td>
                            <td>市网信办</td>
                            <td><span class="status green">是</span></td>
                            <td>24</td>
                            <td>8.2%</td>
                        </tr>
                        <tr>
                            <td>2023-002</td>
                            <td>域名</td>
                            <td class="tooltip">malware.example.com<span class="tooltiptext">恶意软件分发域名</span></td>
                            <td>钓鱼网站</td>
                            <td>2023-04-18</td>
                            <td>拦截并告警</td>
                            <td>公安部</td>
                            <td>省公安厅</td>
                            <td>市公安局</td>
                            <td><span class="status green">是</span></td>
                            <td>56</td>
                            <td>19.1%</td>
                        </tr>
                        <tr>
                            <td>2023-003</td>
                            <td>URL</td>
                            <td class="tooltip">https://malicious.example.org/download<span class="tooltiptext">恶意下载链接</span></td>
                            <td>恶意下载</td>
                            <td>2023-04-22</td>
                            <td>拦截</td>
                            <td>国家网信办</td>
                            <td>省网信办</td>
                            <td>市网信办</td>
                            <td><span class="status red">否</span></td>
                            <td>0</td>
                            <td>0%</td>
                        </tr>
                        <tr>
                            <td>2023-004</td>
                            <td>文件哈希</td>
                            <td class="tooltip">a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6<span class="tooltiptext">恶意文件哈希值</span></td>
                            <td>木马</td>
                            <td>2023-04-25</td>
                            <td>隔离并删除</td>
                            <td>工信部</td>
                            <td>省通信管理局</td>
                            <td>市通信管理局</td>
                            <td><span class="status green">是</span></td>
                            <td>12</td>
                            <td>4.1%</td>
                        </tr>
                        <tr>
                            <td>2023-005</td>
                            <td>IP</td>
                            <td class="tooltip">************<span class="tooltiptext">DDoS攻击源IP</span></td>
                            <td>DDoS攻击源</td>
                            <td>2023-04-28</td>
                            <td>拦截</td>
                            <td>国家网信办</td>
                            <td>省网信办</td>
                            <td>市网信办</td>
                            <td><span class="status green">是</span></td>
                            <td>87</td>
                            <td>29.7%</td>
                        </tr>
                        <tr>
                            <td>2023-006</td>
                            <td>域名</td>
                            <td class="tooltip">ransomware.example.net<span class="tooltiptext">勒索软件控制域名</span></td>
                            <td>勒索软件</td>
                            <td>2023-05-02</td>
                            <td>拦截并告警</td>
                            <td>公安部</td>
                            <td>省公安厅</td>
                            <td>市公安局</td>
                            <td><span class="status green">是</span></td>
                            <td>31</td>
                            <td>10.6%</td>
                        </tr>
                        <tr>
                            <td>2023-007</td>
                            <td>URL</td>
                            <td class="tooltip">https://phishing.example.com/login<span class="tooltiptext">钓鱼网站登录页面</span></td>
                            <td>钓鱼网站</td>
                            <td>2023-05-05</td>
                            <td>拦截</td>
                            <td>工信部</td>
                            <td>省通信管理局</td>
                            <td>市通信管理局</td>
                            <td><span class="status red">否</span></td>
                            <td>0</td>
                            <td>0%</td>
                        </tr>
                        <tr>
                            <td>2023-008</td>
                            <td>文件哈希</td>
                            <td class="tooltip">q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2<span class="tooltiptext">后门程序文件哈希</span></td>
                            <td>后门程序</td>
                            <td>2023-05-08</td>
                            <td>隔离并删除</td>
                            <td>国家网信办</td>
                            <td>省网信办</td>
                            <td>市网信办</td>
                            <td><span class="status green">是</span></td>
                            <td>18</td>
                            <td>6.1%</td>
                        </tr>
                        <tr>
                            <td>2023-009</td>
                            <td>IP</td>
                            <td class="tooltip">*************<span class="tooltiptext">APT组织控制服务器IP</span></td>
                            <td>APT组织</td>
                            <td>2023-05-12</td>
                            <td>拦截并监控</td>
                            <td>公安部</td>
                            <td>省公安厅</td>
                            <td>市公安局</td>
                            <td><span class="status green">是</span></td>
                            <td>42</td>
                            <td>14.3%</td>
                        </tr>
                        <tr>
                            <td>2023-010</td>
                            <td>域名</td>
                            <td class="tooltip">malware-control.example.org<span class="tooltiptext">C&C服务器控制域名</span></td>
                            <td>C&C服务器</td>
                            <td>2023-05-15</td>
                            <td>拦截并告警</td>
                            <td>工信部</td>
                            <td>省通信管理局</td>
                            <td>市通信管理局</td>
                            <td><span class="status green">是</span></td>
                            <td>23</td>
                            <td>7.8%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="pagination">
            <button class="page-btn">«</button>
            <button class="page-btn active">1</button>
            <button class="page-btn">2</button>
            <button class="page-btn">3</button>
            <button class="page-btn">4</button>
            <button class="page-btn">5</button>
            <button class="page-btn">»</button>
        </div>
    </div>

    <script>
        // 移动端菜单切换
        function toggleMenu() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('active');
        }

        // 表格搜索功能
        function searchTable() {
            const input = document.getElementById('searchInput');
            const filter = input.value.toUpperCase();
            const table = document.getElementById('dataTable');
            const tr = table.getElementsByTagName('tr');
            let visibleCount = 0;
            
            for (let i = 1; i < tr.length; i++) {
                let visible = false;
                const td = tr[i].getElementsByTagName('td');
                
                for (let j = 0; j < td.length; j++) {
                    const cell = td[j];
                    if (cell) {
                        const txtValue = cell.textContent || cell.innerText;
                        if (txtValue.toUpperCase().indexOf(filter) > -1) {
                            visible = true;
                            break;
                        }
                    }
                }
                
                if (visible) {
                    tr[i].style.display = '';
                    visibleCount++;
                } else {
                    tr[i].style.display = 'none';
                }
            }
            
            document.getElementById('visibleRows').textContent = visibleCount;
        }

        // 表格排序功能
        let sortColumn = -1;
        let sortDirection = 1; // 1 for ascending, -1 for descending

        function sortTable(column) {
            const table = document.getElementById('dataTable');
            const headers = table.getElementsByTagName('th');
            
            // 重置所有表头的排序标记
            for (let i = 0; i < headers.length; i++) {
                headers[i].classList.remove('sort-asc', 'sort-desc');
            }
            
            // 设置新的排序方向
            if (sortColumn === column) {
                sortDirection *= -1;
            } else {
                sortDirection = 1;
                sortColumn = column;
            }
            
            // 添加排序标记
            if (sortDirection === 1) {
                headers[column].classList.add('sort-asc');
            } else {
                headers[column].classList.add('sort-desc');
            }
            
            const tbody = table.getElementsByTagName('tbody')[0];
            const rows = Array.from(tbody.getElementsByTagName('tr'));
            
            // 排序行
            rows.sort((a, b) => {
                const aValue = a.getElementsByTagName('td')[column].innerText;
                const bValue = b.getElementsByTagName('td')[column].innerText;
                
                // 检查是否为数字
                const aNum = parseFloat(aValue.replace(/[^0-9.-]+/g, ''));
                const bNum = parseFloat(bValue.replace(/[^0-9.-]+/g, ''));
                
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return sortDirection * (aNum - bNum);
                } else {
                    return sortDirection * aValue.localeCompare(bValue);
                }
            });
            
            // 重新排列行
            for (const row of rows) {
                tbody.appendChild(row);
            }
        }

        // 添加筛选标签
        function addFilter(type, value) {
            const filterTags = document.getElementById('filterTags');
            const tagId = `filter-${type}-${value}`.replace(/\s+/g, '-');
            
            // 检查标签是否已存在
            if (!document.getElementById(tagId)) {
                const tag = document.createElement('div');
                tag.id = tagId;
                tag.className = 'filter-tag';
                tag.innerHTML = `${type}: ${value} <span class="close" onclick="removeFilter('${tagId}')">×</span>`;
                filterTags.appendChild(tag);
                
                // 应用筛选
                applyFilters();
            }
        }

        // 移除筛选标签
        function removeFilter(tagId) {
            const tag = document.getElementById(tagId);
            if (tag) {
                tag.remove();
                applyFilters();
            }
        }

        // 应用所有筛选条件
        function applyFilters() {
            const table = document.getElementById('dataTable');
            const tr = table.getElementsByTagName('tr');
            const filterTags = document.getElementById('filterTags').getElementsByClassName('filter-tag');
            let visibleCount = 0;
            
            // 如果没有筛选标签，显示所有行
            if (filterTags.length === 0) {
                for (let i = 1; i < tr.length; i++) {
                    tr[i].style.display = '';
                    visibleCount++;
                }
                document.getElementById('visibleRows').textContent = visibleCount;
                return;
            }
            
            // 应用筛选
            for (let i = 1; i < tr.length; i++) {
                let visible = true;
                const td = tr[i].getElementsByTagName('td');
                
                // 检查每个筛选条件
                for (let j = 0; j < filterTags.length; j++) {
                    const filterText = filterTags[j].textContent.trim();
                    const [type, value] = filterText.split(':').map(item => item.trim().replace('×', ''));
                    
                    let columnIndex = -1;
                    // 确定列索引
                    const headers = table.getElementsByTagName('th');
                    for (let k = 0; k < headers.length; k++) {
                        if (headers[k].textContent === type) {
                            columnIndex = k;
                            break;
                        }
                    }
                    
                    if (columnIndex !== -1) {
                        const cellValue = td[columnIndex].textContent || td[columnIndex].innerText;
                        if (cellValue.indexOf(value) === -1) {
                            visible = false;
                            break;
                        }
                    }
                }
                
                if (visible) {
                    tr[i].style.display = '';
                    visibleCount++;
                } else {
                    tr[i].style.display = 'none';
                }
            }
            
            document.getElementById('visibleRows').textContent = visibleCount;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置总行数
            const table = document.getElementById('dataTable');
            const rowCount = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr').length;
            document.getElementById('totalRows').textContent = rowCount;
            document.getElementById('visibleRows').textContent = rowCount;
            
            // 为分页按钮添加点击事件
            const pageButtons = document.getElementsByClassName('page-btn');
            for (let i = 0; i < pageButtons.length; i++) {
                pageButtons[i].addEventListener('click', function() {
                    for (let j = 0; j < pageButtons.length; j++) {
                        pageButtons[j].classList.remove('active');
                    }
                    this.classList.add('active');
                });
            }
            
            // 为文件导入添加事件监听
            document.getElementById('fileInput').addEventListener('change', handleFileImport);
        });
        
        // 处理文件导入
        function handleFileImport(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const fileName = file.name;
            const fileExt = fileName.split('.').pop().toLowerCase();
            
            // 显示导入中的提示
            alert(`正在导入文件: ${fileName}`);
            
            // 这里可以根据文件类型进行不同的处理
            if (fileExt === 'csv') {
                // 处理CSV文件
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        // 这里只是模拟导入，实际项目中需要解析CSV并更新表格
                        setTimeout(() => {
                            alert('CSV文件导入成功！');
                            // 重置文件输入框，允许再次选择同一文件
                            document.getElementById('fileInput').value = '';
                        }, 1000);
                    } catch (error) {
                        alert('导入失败: ' + error.message);
                    }
                };
                reader.readAsText(file);
            } else if (fileExt === 'xlsx') {
                // 处理Excel文件
                alert('Excel文件导入功能需要额外的库支持，此处为演示');
                setTimeout(() => {
                    alert('Excel文件导入成功！');
                    document.getElementById('fileInput').value = '';
                }, 1000);
            } else if (fileExt === 'json') {
                // 处理JSON文件
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        // 这里只是模拟导入，实际项目中需要解析JSON并更新表格
                        setTimeout(() => {
                            alert('JSON文件导入成功！');
                            document.getElementById('fileInput').value = '';
                        }, 1000);
                    } catch (error) {
                        alert('导入失败: ' + error.message);
                    }
                };
                reader.readAsText(file);
            } else {
                alert('不支持的文件格式，请上传CSV、XLSX或JSON文件');
            }
        }
    </script>
</body>
</html>