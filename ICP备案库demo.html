<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICP备案库</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        body {
            display: flex;
            background-color: #f5f6fa;
            min-height: 100vh;
        }
        .sidebar {
            width: 200px;
            background-color: #fff;
            height: 100vh;
            border-right: 1px solid #e0e0e0;
            position: sticky;
            top: 0;
            overflow-y: auto;
            transition: all 0.3s ease;
        }
        .logo {
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
            text-align: center;
            font-size: 14px;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .logo-icon {
            margin-right: 8px;
            font-size: 18px;
        }
        .menu {
            list-style: none;
        }
        .menu-item {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            position: relative;
            transition: all 0.2s ease;
        }
        .menu-item.active {
            background-color: #f0f6ff;
            color: #4285f4;
            border-left: 3px solid #4285f4;
        }
        .menu-item:hover {
            background-color: #f9f9f9;
        }
        .menu-item i {
            margin-right: 8px;
            width: 20px;
            text-align: center;
        }
        .submenu {
            list-style: none;
            padding-left: 20px;
            display: none;
            background-color: #fafafa;
        }
        .menu-item.active .submenu {
            display: block;
        }
        .submenu-item {
            padding: 10px 15px;
            font-size: 13px;
            color: #666;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .submenu-item.active {
            color: #4285f4;
            font-weight: 500;
        }
        .submenu-item:hover {
            color: #4285f4;
            background-color: #f5f5f5;
        }
        .content {
            flex: 1;
            padding: 20px;
            overflow-x: auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .title {
            font-size: 24px;
            color: #333;
            font-weight: bold;
            margin-bottom: 10px;
        }
        /* 调整表格列宽 */
        table th:nth-child(1), table td:nth-child(1) { width: 15%; } /* 网站名称 */
        table th:nth-child(2), table td:nth-child(2) { width: 15%; } /* 网站域名 */
        table th:nth-child(3), table td:nth-child(3) { width: 12%; } /* 备案许可证 */
        table th:nth-child(4), table td:nth-child(4) { width: 12%; } /* 主办者 */
        table th:nth-child(5), table td:nth-child(5) { width: 12%; } /* 营业地 */
        table th:nth-child(6), table td:nth-child(6) { width: 12%; } /* 接入地 */
        table th:nth-child(7), table td:nth-child(7) { width: 22%; } /* 办公地址 */
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 4px;
            padding: 15px;
            flex: 1;
            min-width: 200px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .stat-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .stat-value.green {
            color: #34a853;
        }
        .stat-value.blue {
            color: #4285f4;
        }
        .stat-value.orange {
            color: #fbbc05;
        }
        .stat-value.red {
            color: #ea4335;
        }
        .search-bar {
            display: flex;
            margin-bottom: 20px;
            gap: 10px;
            flex-wrap: wrap;
        }
        .search-input {
            flex: 1;
            min-width: 200px;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }
        .search-input:focus {
            border-color: #4285f4;
            outline: none;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }
        .search-btn {
            padding: 10px 20px;
            background-color: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s ease;
        }
        .search-btn:hover {
            background-color: #3367d6;
        }
        .table-container {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }
        .table-responsive {
            overflow-x: auto;
            width: 100%;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }
        th {
            background-color: #f9f9f9;
            color: #333;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
            cursor: pointer;
        }
        th:hover {
            background-color: #f0f0f0;
        }
        th::after {
            content: '⇅';
            margin-left: 5px;
            color: #aaa;
            font-size: 12px;
        }
        th.sort-asc::after {
            content: '↑';
            color: #4285f4;
        }
        th.sort-desc::after {
            content: '↓';
            color: #4285f4;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .pagination {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
            gap: 5px;
            flex-wrap: wrap;
        }
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background-color: #fff;
            cursor: pointer;
            font-size: 14px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        .page-btn.active {
            background-color: #4285f4;
            color: white;
            border-color: #4285f4;
        }
        .page-btn:hover:not(.active) {
            background-color: #f5f5f5;
        }
        .status {
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            text-align: center;
            min-width: 60px;
        }
        .status.green {
            background-color: #e6f4ea;
            color: #34a853;
        }
        .status.red {
            background-color: #fce8e6;
            color: #ea4335;
        }
        .status.orange {
            background-color: #fef7e0;
            color: #fbbc05;
        }
        .dropdown {
            position: relative;
            display: inline-block;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
            border-radius: 4px;
            overflow: hidden;
        }
        .dropdown:hover .dropdown-content {
            display: block;
        }
        .dropdown-item {
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            color: #333;
            font-size: 14px;
            transition: background-color 0.2s ease;
        }
        .dropdown-item:hover {
            background-color: #f1f1f1;
        }
        .filter-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        .filter-tag {
            background-color: #e8f0fe;
            color: #4285f4;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        .filter-tag .close {
            margin-left: 5px;
            font-weight: bold;
        }
        .filter-tag:hover {
            background-color: #d2e3fc;
        }
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #333;
        }
        .table-actions {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        .table-info {
            font-size: 14px;
            color: #666;
        }
        .export-btn {
            padding: 8px 15px;
            background-color: #34a853;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: background-color 0.2s ease;
        }
        .export-btn:hover {
            background-color: #2d9144;
        }
        .import-btn {
            padding: 8px 15px;
            background-color: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            margin-right: 10px;
            transition: background-color 0.2s ease;
        }
        .import-btn:hover {
            background-color: #3367d6;
        }
        .header-actions {
            display: flex;
            gap: 10px;
        }
        .file-input {
            display: none;
        }
        .tooltip {
            position: relative;
            display: inline-block;
            width: 100%;
            cursor: help;
        }
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 250px;
            background-color: #333;
            color: #fff;
            text-align: left;
            border-radius: 4px;
            padding: 8px;
            position: absolute;
            z-index: 100;
            bottom: 125%;
            left: 0;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            word-wrap: break-word;
            white-space: normal;
        }
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                flex-direction: column;
            }
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                display: none;
            }
            .sidebar.active {
                display: block;
            }
            .mobile-menu-toggle {
                display: block;
                margin-right: 10px;
            }
            .header {
                flex-direction: column;
                align-items: flex-start;
            }
            .stats {
                flex-direction: column;
            }
            .stat-card {
                width: 100%;
            }
            .search-bar {
                flex-direction: column;
            }
            .search-input {
                width: 100%;
            }
            .table-responsive {
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
    <button class="mobile-menu-toggle" onclick="toggleMenu()">☰</button>
    
    <div class="sidebar" id="sidebar">
        <div class="logo">
            <span class="logo-icon">🔒</span>
            <span>网络安全威胁情报系统</span>
        </div>
        <ul class="menu">
            <li class="menu-item">
                <i>📊</i> 情报总览
            </li>
            <li class="menu-item">
                <i>🔍</i> 情报查询
            </li>
            <li class="menu-item active">
                <i>📚</i> 情报库
                <ul class="submenu">
                    <li class="submenu-item">本地威胁情报库</li>
                    <li class="submenu-item">上级单位共享威胁情报库</li>
                    <li class="submenu-item active">ICP备案库</li>
                </ul>
            </li>
            <li class="menu-item">
                <i>📝</i> 情报分析
            </li>
            <li class="menu-item">
                <i>🔔</i> 预警通知
            </li>
            <li class="menu-item">
                <i>⚙️</i> 系统设置
            </li>
        </ul>
    </div>
    
    <div class="content">
        <div class="header">
            <h1 class="title">ICP备案库</h1>
            <div class="header-actions">
                <input type="file" id="fileInput" class="file-input" accept=".csv,.xlsx,.json">
                <button class="import-btn" onclick="document.getElementById('fileInput').click()"><i>📤</i> 导入数据</button>
                <button class="export-btn"><i>📥</i> 导出数据</button>
            </div>
        </div>
        
        <div class="stats-cards">
            <div class="stats-card">
                <div class="stats-card-title">网站总数</div>
                <div class="stats-card-value">10 条</div>
            </div>
            <div class="stats-card">
                <div class="stats-card-title">本月新增</div>
                <div class="stats-card-value">2 条</div>
            </div>
            <div class="stats-card">
                <div class="stats-card-title">今日更新时间</div>
                <div class="stats-card-value">2023-04-19T09:15:37.000+00:00</div>
            </div>
            <div class="stats-card">
                <div class="stats-card-title">更新状态</div>
                <div class="stats-card-value status-normal">更新成功</div>
            </div>
        </div>
        
        <div class="search-bar">
            <input type="text" class="search-input" id="searchInput" placeholder="搜索网站名称、网站域名、备案许可证、主办者等..." onkeyup="searchTable()">
            <div class="dropdown">
                <button class="search-btn">高级筛选 ▼</button>
                <div class="dropdown-content">
                    <a href="#" class="dropdown-item" onclick="addFilter('网站域名', '.com')">按域名后缀筛选</a>
                    <a href="#" class="dropdown-item" onclick="addFilter('主办者', '政府机构')">按主办者类型筛选</a>
                    <a href="#" class="dropdown-item" onclick="addFilter('营业地', '山东省')">按营业地筛选</a>
                    <a href="#" class="dropdown-item" onclick="addFilter('接入地', '济南市')">按接入地筛选</a>
                </div>
            </div>
            <button class="search-btn" onclick="searchTable()">搜索</button>
        </div>
        
        <div class="filter-tags" id="filterTags">
            <!-- 筛选标签会动态添加到这里 -->
        </div>
        
        <div class="table-actions">
            <div class="table-info">显示 <span id="visibleRows">10</span> 条记录，共 <span id="totalRows">10</span> 条</div>
        </div>
        
        <div class="table-container">
            <div class="table-responsive">
                <table id="dataTable">
                    <thead>
                        <tr>
                            <th onclick="sortTable(0)">网站名称</th>
                            <th onclick="sortTable(1)">网站域名</th>
                            <th onclick="sortTable(2)">备案许可证</th>
                            <th onclick="sortTable(3)">主办者</th>
                            <th onclick="sortTable(4)">营业地</th>
                            <th onclick="sortTable(5)">接入地</th>
                            <th onclick="sortTable(6)">办公地址</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>济南市人民政府门户网站</td>
                            <td>www.jinan.gov.cn</td>
                            <td>鲁ICP备05039506号-1</td>
                            <td>济南市人民政府办公厅</td>
                            <td>山东省</td>
                            <td>济南市</td>
                            <td>济南市历下区龙鼎大道1号龙奥大厦</td>
                        </tr>
                        <tr>
                            <td>济南日报</td>
                            <td>www.jnrb.com.cn</td>
                            <td>鲁ICP备09082651号-1</td>
                            <td>济南日报社</td>
                            <td>山东省</td>
                            <td>济南市</td>
                            <td>济南市市中区纬二路51号</td>
                        </tr>
                        <tr>
                            <td>济南市教育局</td>
                            <td>jyj.jinan.gov.cn</td>
                            <td>鲁ICP备05039506号-2</td>
                            <td>济南市教育局</td>
                            <td>山东省</td>
                            <td>济南市</td>
                            <td>济南市市中区经七路73号</td>
                        </tr>
                        <tr>
                            <td>济南市公安局</td>
                            <td>jnga.jinan.gov.cn</td>
                            <td>鲁ICP备05039506号-3</td>
                            <td>济南市公安局</td>
                            <td>山东省</td>
                            <td>济南市</td>
                            <td>济南市市中区经七路73号</td>
                        </tr>
                        <tr>
                            <td>济南市卫生健康委员会</td>
                            <td>jnwsjkw.jinan.gov.cn</td>
                            <td>鲁ICP备05039506号-4</td>
                            <td>济南市卫生健康委员会</td>
                            <td>山东省</td>
                            <td>济南市</td>
                            <td>济南市市中区经七路89号</td>
                        </tr>
                        <tr>
                            <td>济南市文化和旅游局</td>
                            <td>whlyjj.jinan.gov.cn</td>
                            <td>鲁ICP备05039506号-5</td>
                            <td>济南市文化和旅游局</td>
                            <td>山东省</td>
                            <td>济南市</td>
                            <td>济南市历下区龙鼎大道1号龙奥大厦</td>
                        </tr>
                        <tr>
                            <td>济南市市场监督管理局</td>
                            <td>scjgj.jinan.gov.cn</td>
                            <td>鲁ICP备05039506号-6</td>
                            <td>济南市市场监督管理局</td>
                            <td>山东省</td>
                            <td>济南市</td>
                            <td>济南市历下区龙鼎大道1号龙奥大厦</td>
                        </tr>
                        <tr>
                            <td>济南市生态环境局</td>
                            <td>sthjj.jinan.gov.cn</td>
                            <td>鲁ICP备05039506号-7</td>
                            <td>济南市生态环境局</td>
                            <td>山东省</td>
                            <td>济南市</td>
                            <td>济南市历下区龙鼎大道1号龙奥大厦</td>
                        </tr>
                        <tr>
                            <td>济南市住房和城乡建设局</td>
                            <td>jnzfcxjsj.jinan.gov.cn</td>
                            <td>鲁ICP备05039506号-8</td>
                            <td>济南市住房和城乡建设局</td>
                            <td>山东省</td>
                            <td>济南市</td>
                            <td>济南市历下区龙鼎大道1号龙奥大厦</td>
                        </tr>
                        <tr>
                            <td>济南市交通运输局</td>
                            <td>jtysj.jinan.gov.cn</td>
                            <td>鲁ICP备05039506号-9</td>
                            <td>济南市交通运输局</td>
                            <td>山东省</td>
                            <td>济南市</td>
                            <td>济南市历下区龙鼎大道1号龙奥大厦</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="pagination">
            <button class="page-btn">«</button>
            <button class="page-btn active">1</button>
            <button class="page-btn">2</button>
            <button class="page-btn">3</button>
            <button class="page-btn">4</button>
            <button class="page-btn">5</button>
            <button class="page-btn">»</button>
        </div>
    </div>
    
    <script>
        function toggleMenu() {
            document.getElementById('sidebar').classList.toggle('active');
        }
        
        function searchTable() {
            // 搜索功能实现
            var input, filter, table, tr, td, i, j, txtValue, found;
            input = document.getElementById("searchInput");
            filter = input.value.toUpperCase();
            table = document.getElementById("dataTable");
            tr = table.getElementsByTagName("tr");
            
            var visibleCount = 0;
            var totalCount = tr.length - 1; // 减去表头行
            
            for (i = 1; i < tr.length; i++) {
                found = false;
                for (j = 0; j < 7; j++) { // 搜索所有列
                    td = tr[i].getElementsByTagName("td")[j];
                    if (td) {
                        txtValue = td.textContent || td.innerText;
                        if (txtValue.toUpperCase().indexOf(filter) > -1) {
                            found = true;
                            break;
                        }
                    }
                }
                if (found) {
                    tr[i].style.display = "";
                    visibleCount++;
                } else {
                    tr[i].style.display = "none";
                }
            }
            
            document.getElementById("visibleRows").textContent = visibleCount;
            document.getElementById("totalRows").textContent = totalCount;
        }
        
        function sortTable(n) {
            var table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
            table = document.getElementById("dataTable");
            
            // 清除所有表头的排序状态
            var headers = table.getElementsByTagName("th");
            for (i = 0; i < headers.length; i++) {
                headers[i].classList.remove("sort-asc", "sort-desc");
            }
            
            switching = true;
            dir = "asc"; 
            
            while (switching) {
                switching = false;
                rows = table.rows;
                
                for (i = 1; i < (rows.length - 1); i++) {
                    shouldSwitch = false;
                    x = rows[i].getElementsByTagName("td")[n];
                    y = rows[i + 1].getElementsByTagName("td")[n];
                    
                    if (dir == "asc") {
                        if (x.innerHTML.toLowerCase() > y.innerHTML.toLowerCase()) {
                            shouldSwitch = true;
                            break;
                        }
                    } else if (dir == "desc") {
                        if (x.innerHTML.toLowerCase() < y.innerHTML.toLowerCase()) {
                            shouldSwitch = true;
                            break;
                        }
                    }
                }
                
                if (shouldSwitch) {
                    rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                    switching = true;
                    switchcount++;
                } else {
                    if (switchcount == 0 && dir == "asc") {
                        dir = "desc";
                        switching = true;
                    }
                }
            }
            
            // 设置当前排序列的状态
            if (dir == "asc") {
                headers[n].classList.add("sort-desc");
            } else {
                headers[n].classList.add("sort-asc");
            }
        }
        
        function addFilter(type, value) {
            var filterTags = document.getElementById("filterTags");
            var tag = document.createElement("div");
            tag.className = "filter-tag";
            tag.innerHTML = type + ": " + value + " <span class='close' onclick='removeFilter(this)'>×</span>";
            filterTags.appendChild(tag);
            
            // 应用筛选
            applyFilters();
        }
        
        function removeFilter(element) {
            element.parentNode.remove();
            
            // 重新应用筛选
            applyFilters();
        }
        
        function applyFilters() {
            // 筛选功能实现
            var filters = document.getElementsByClassName("filter-tag");
            var table = document.getElementById("dataTable");
            var tr = table.getElementsByTagName("tr");
            
            var visibleCount = 0;
            var totalCount = tr.length - 1; // 减去表头行
            
            // 如果没有筛选标签，显示所有行
            if (filters.length === 0) {
                for (var i = 1; i < tr.length; i++) {
                    tr[i].style.display = "";
                    visibleCount++;
                }
            } else {
                for (var i = 1; i < tr.length; i++) {
                    var display = true;
                    
                    for (var j = 0; j < filters.length; j++) {
                        var filterText = filters[j].textContent.trim();
                        var colonIndex = filterText.indexOf(":");
                        var type = filterText.substring(0, colonIndex).trim();
                        var value = filterText.substring(colonIndex + 1, filterText.length - 1).trim();
                        
                        var columnIndex;
                        switch (type) {
                            case "网站域名": columnIndex = 1; break;
                            case "主办者": columnIndex = 3; break;
                            case "营业地": columnIndex = 4; break;
                            case "接入地": columnIndex = 5; break;
                            default: columnIndex = -1;
                        }
                        
                        if (columnIndex >= 0) {
                            var td = tr[i].getElementsByTagName("td")[columnIndex];
                            if (td) {
                                var txtValue = td.textContent || td.innerText;
                                if (txtValue.toUpperCase().indexOf(value.toUpperCase()) === -1) {
                                    display = false;
                                    break;
                                }
                            }
                        }
                    }
                    
                    if (display) {
                        tr[i].style.display = "";
                        visibleCount++;
                    } else {
                        tr[i].style.display = "none";
                    }
                }
            }
            
            document.getElementById("visibleRows").textContent = visibleCount;
            document.getElementById("totalRows").textContent = totalCount;
        }
        
        // 初始化
        document.addEventListener("DOMContentLoaded", function() {
            document.getElementById("visibleRows").textContent = document.getElementById("dataTable").getElementsByTagName("tr").length - 1;
            document.getElementById("totalRows").textContent = document.getElementById("dataTable").getElementsByTagName("tr").length - 1;
        });
    </script>
</body>
</html>