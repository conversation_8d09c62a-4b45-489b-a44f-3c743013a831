<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据安全研判分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        body {
            display: flex;
            background-color: #f5f6fa;
            min-height: 100vh;
        }
        .sidebar {
            width: 220px;
            background-color: #001529;
            height: 100vh;
            position: sticky;
            top: 0;
            overflow-y: auto;
            transition: all 0.3s ease;
            color: #fff;
        }
        .logo {
            padding: 20px;
            border-bottom: 1px solid #1a2d3d;
            text-align: center;
            font-size: 16px;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .logo-icon {
            margin-right: 10px;
            font-size: 20px;
        }
        .menu {
            list-style: none;
        }
        .menu-item {
            padding: 14px 20px;
            border-bottom: 1px solid #1a2d3d;
            cursor: pointer;
            font-size: 14px;
            color: #a6adb4;
            position: relative;
            transition: all 0.2s ease;
        }
        .menu-item.active {
            background-color: #1890ff;
            color: #fff;
        }
        .menu-item:hover {
            background-color: #1a2d3d;
            color: #fff;
        }
        .menu-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .submenu {
            list-style: none;
            padding-left: 20px;
            display: none;
            background-color: #000c17;
        }
        .menu-item.active .submenu {
            display: block;
        }
        .submenu-item {
            padding: 12px 15px;
            font-size: 13px;
            color: #a6adb4;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .submenu-item.active {
            color: #1890ff;
            font-weight: 500;
        }
        .submenu-item:hover {
            color: #fff;
        }
        .content {
            flex: 1;
            padding: 20px;
            overflow-x: auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .title {
            font-size: 24px;
            color: #333;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 4px;
            padding: 20px;
            flex: 1;
            min-width: 220px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .stat-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .stat-value.green {
            color: #52c41a;
        }
        .stat-value.blue {
            color: #1890ff;
        }
        .stat-value.orange {
            color: #faad14;
        }
        .stat-value.red {
            color: #f5222d;
        }
        .search-bar {
            display: flex;
            margin-bottom: 20px;
            gap: 10px;
            flex-wrap: wrap;
        }
        .search-input {
            flex: 1;
            min-width: 200px;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }
        .search-input:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        .search-btn {
            padding: 10px 20px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s ease;
        }
        .search-btn:hover {
            background-color: #40a9ff;
        }
        .table-container {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }
        .table-responsive {
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        th {
            background-color: #fafafa;
            font-weight: 500;
            color: #333;
            white-space: nowrap;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .status {
            padding: 4px 8px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
        }
        .status.green {
            background-color: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .status.orange {
            background-color: #fff7e6;
            color: #faad14;
            border: 1px solid #ffd591;
        }
        .status.red {
            background-color: #fff1f0;
            color: #f5222d;
            border: 1px solid #ffa39e;
        }
        .chart-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .chart {
            background-color: #fff;
            border-radius: 4px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            flex: 1;
            min-width: 300px;
            min-height: 300px;
        }
        .chart-title {
            font-size: 16px;
            color: #333;
            margin-bottom: 15px;
            font-weight: 500;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #eee;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            transition: all 0.2s ease;
        }
        .tab.active {
            color: #1890ff;
            border-bottom: 2px solid #1890ff;
        }
        .tab:hover {
            color: #1890ff;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .alert-level {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .alert-level.high {
            background-color: #f5222d;
        }
        .alert-level.medium {
            background-color: #faad14;
        }
        .alert-level.low {
            background-color: #52c41a;
        }
        .pagination {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        .page-item {
            margin: 0 5px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .page-item.active {
            background-color: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }
        .page-item:hover:not(.active) {
            border-color: #1890ff;
            color: #1890ff;
        }
        /* 图表模拟样式 */
        .chart-placeholder {
            width: 100%;
            height: 250px;
            background-color: #f9f9f9;
            border: 1px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
        }
        /* 地图容器 */
        .map-container {
            width: 100%;
            height: 400px;
            background-color: #f9f9f9;
            border: 1px solid #eee;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }
        .map-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI4MDAiIGhlaWdodD0iNDAwIj48cmVjdCB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iI2Y5ZjlmOSIvPjxwYXRoIGQ9Ik0xMDAgMTAwIEwxNTAgMTUwIEwyMDAgMTIwIEwyNTAgMTgwIEwzMDAgMTUwIEwzNTAgMjAwIEw0MDAgMTcwIEw0NTAgMjIwIEw1MDAgMTgwIEw1NTAgMjMwIEw2MDAgMjAwIEw2NTAgMjUwIEw3MDAgMjIwIiBzdHJva2U9IiNkZGQiIGZpbGw9Im5vbmUiIHN0cm9rZS13aWR0aD0iMiIvPjxjaXJjbGUgY3g9IjIwMCIgY3k9IjEyMCIgcj0iNSIgZmlsbD0iI2Y1MjIyZCIvPjxjaXJjbGUgY3g9IjMwMCIgY3k9IjE1MCIgcj0iNSIgZmlsbD0iI2ZhYWQxNCIvPjxjaXJjbGUgY3g9IjQwMCIgY3k9IjE3MCIgcj0iNSIgZmlsbD0iI2Y1MjIyZCIvPjxjaXJjbGUgY3g9IjUwMCIgY3k9IjE4MCIgcj0iNSIgZmlsbD0iI2ZhYWQxNCIvPjxjaXJjbGUgY3g9IjYwMCIgY3k9IjIwMCIgcj0iNSIgZmlsbD0iI2Y1MjIyZCIvPjwvc3ZnPg==');
            background-size: cover;
        }
        /* 数据点样式 */
        .data-point {
            position: absolute;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #f5222d;
            transform: translate(-50%, -50%);
            cursor: pointer;
            box-shadow: 0 0 0 2px rgba(255,255,255,0.8);
        }
        .data-point.medium {
            background-color: #faad14;
        }
        .data-point.low {
            background-color: #52c41a;
        }
        /* 数据流向线 */
        .data-flow {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, rgba(24,144,255,0.2), rgba(24,144,255,0.8));
            transform-origin: left center;
        }
        /* 响应式调整 */
        @media (max-width: 768px) {
            .sidebar {
                width: 60px;
            }
            .logo span, .menu-item span {
                display: none;
            }
            .content {
                padding: 10px;
            }
            .stat-card {
                min-width: 100%;
            }
            .chart {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="logo">
            <i class="logo-icon">🔒</i>
            <span>数据安全中心</span>
        </div>
        <ul class="menu">
            <li class="menu-item">
                <i>📊</i>
                <span>总览</span>
            </li>
            <li class="menu-item active">
                <i>🔍</i>
                <span>数据安全研判分析</span>
                <ul class="submenu">
                    <li class="submenu-item active">暗网数据监测</li>
                    <li class="submenu-item">互联网暴露面监测</li>
                    <li class="submenu-item">异常跨境数据研判分析</li>
                </ul>
            </li>
            <li class="menu-item">
                <i>🛡️</i>
                <span>安全防护</span>
            </li>
            <li class="menu-item">
                <i>⚠️</i>
                <span>威胁情报</span>
            </li>
            <li class="menu-item">
                <i>📝</i>
                <span>合规管理</span>
            </li>
            <li class="menu-item">
                <i>⚙️</i>
                <span>系统设置</span>
            </li>
        </ul>
    </div>

    <div class="content">
        <div class="header">
            <div class="title">数据安全研判分析 - 暗网数据监测</div>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-title">今日暗网敏感信息</div>
                <div class="stat-value red">28</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">本周暗网敏感信息</div>
                <div class="stat-value orange">156</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">高危信息占比</div>
                <div class="stat-value blue">32.5%</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">已处置信息</div>
                <div class="stat-value green">142</div>
            </div>
        </div>

        <div class="tabs">
            <div class="tab active">暗网数据监测</div>
            <div class="tab">互联网暴露面监测</div>
            <div class="tab">异常跨境数据研判分析</div>
        </div>

        <div class="tab-content active" id="darknet-monitoring">
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="输入关键词、单位名称或IP地址搜索">
                <button class="search-btn">搜索</button>
            </div>

            <div class="chart-container">
                <div class="chart">
                    <div class="chart-title">暗网敏感信息类型分布</div>
                    <div class="chart-placeholder">
                        <svg width="100%" height="100%" viewBox="0 0 400 250">
                            <!-- 饼图模拟 -->
                            <circle cx="150" cy="125" r="80" fill="#f5f5f5" stroke="#ddd" />
                            <path d="M 150 125 L 150 45 A 80 80 0 0 1 223 162 Z" fill="#f5222d" />
                            <path d="M 150 125 L 223 162 A 80 80 0 0 1 150 205 Z" fill="#faad14" />
                            <path d="M 150 125 L 150 205 A 80 80 0 0 1 77 162 Z" fill="#1890ff" />
                            <path d="M 150 125 L 77 162 A 80 80 0 0 1 150 45 Z" fill="#52c41a" />
                            
                            <!-- 图例 -->
                            <rect x="260" y="80" width="15" height="15" fill="#f5222d" />
                            <text x="285" y="93" font-size="12" fill="#666">数据泄露 (35%)</text>
                            
                            <rect x="260" y="110" width="15" height="15" fill="#faad14" />
                            <text x="285" y="123" font-size="12" fill="#666">黑客攻击 (25%)</text>
                            
                            <rect x="260" y="140" width="15" height="15" fill="#1890ff" />
                            <text x="285" y="153" font-size="12" fill="#666">账号交易 (22%)</text>
                            
                            <rect x="260" y="170" width="15" height="15" fill="#52c41a" />
                            <text x="285" y="183" font-size="12" fill="#666">其他 (18%)</text>
                        </svg>
                    </div>
                </div>
                <div class="chart">
                    <div class="chart-title">暗网敏感信息趋势（近30天）</div>
                    <div class="chart-placeholder">
                        <svg width="100%" height="100%" viewBox="0 0 500 250">
                            <!-- 坐标轴 -->
                            <line x1="50" y1="200" x2="450" y2="200" stroke="#ddd" stroke-width="1" />
                            <line x1="50" y1="50" x2="50" y2="200" stroke="#ddd" stroke-width="1" />
                            
                            <!-- 折线图 -->
                            <polyline points="50,180 80,170 110,175 140,160 170,150 200,155 230,130 260,140 290,120 320,100 350,110 380,90 410,70 440,80" 
                                    fill="none" stroke="#f5222d" stroke-width="2" />
                            
                            <!-- 数据点 -->
                            <circle cx="50" cy="180" r="3" fill="#f5222d" />
                            <circle cx="80" cy="170" r="3" fill="#f5222d" />
                            <circle cx="110" cy="175" r="3" fill="#f5222d" />
                            <circle cx="140" cy="160" r="3" fill="#f5222d" />
                            <circle cx="170" cy="150" r="3" fill="#f5222d" />
                            <circle cx="200" cy="155" r="3" fill="#f5222d" />
                            <circle cx="230" cy="130" r="3" fill="#f5222d" />
                            <circle cx="260" cy="140" r="3" fill="#f5222d" />
                            <circle cx="290" cy="120" r="3" fill="#f5222d" />
                            <circle cx="320" cy="100" r="3" fill="#f5222d" />
                            <circle cx="350" cy="110" r="3" fill="#f5222d" />
                            <circle cx="380" cy="90" r="3" fill="#f5222d" />
                            <circle cx="410" cy="70" r="3" fill="#f5222d" />
                            <circle cx="440" cy="80" r="3" fill="#f5222d" />
                            
                            <!-- 坐标轴标签 -->
                            <text x="45" y="220" font-size="10" text-anchor="end" fill="#999">1日</text>
                            <text x="140" y="220" font-size="10" text-anchor="middle" fill="#999">10日</text>
                            <text x="230" y="220" font-size="10" text-anchor="middle" fill="#999">15日</text>
                            <text x="320" y="220" font-size="10" text-anchor="middle" fill="#999">20日</text>
                            <text x="410" y="220" font-size="10" text-anchor="middle" fill="#999">25日</text>
                            
                            <text x="40" y="200" font-size="10" text-anchor="end" fill="#999">0</text>
                            <text x="40" y="150" font-size="10" text-anchor="end" fill="#999">10</text>
                            <text x="40" y="100" font-size="10" text-anchor="end" fill="#999">20</text>
                            <text x="40" y="50" font-size="10" text-anchor="end" fill="#999">30</text>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="table-container">
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>发现时间</th>
                                <th>涉及单位</th>
                                <th>信息类型</th>
                                <th>数据泄露标题</th>
                                <th>暗网地址</th>
                                <th>风险等级</th>
                                <th>处置状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2023-06-15 08:23</td>
                                <td>济南市人社局</td>
                                <td>数据泄露</td>
                                <td>济南市人社局员工信息数据出售</td>
                                <td>onion://7hj9f...</td>
                                <td><span class="alert-level high"></span> 高危</td>
                                <td><span class="status orange">处置中</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>2023-06-14 22:45</td>
                                <td>济南市公共资源交易中心</td>
                                <td>账号交易</td>
                                <td>济南市公共资源交易平台账号批量出售</td>
                                <td>onion://3rf5d...</td>
                                <td><span class="alert-level medium"></span> 中危</td>
                                <td><span class="status green">已处置</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>2023-06-14 16:12</td>
                                <td>济南市大数据局</td>
                                <td>黑客攻击</td>
                                <td>济南市大数据局服务器漏洞利用工具出售</td>
                                <td>onion://9ju7g...</td>
                                <td><span class="alert-level high"></span> 高危</td>
                                <td><span class="status red">未处置</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>2023-06-13 09:34</td>
                                <td>济南市卫健委</td>
                                <td>数据泄露</td>
                                <td>济南市医疗机构患者数据样本出售</td>
                                <td>onion://2hy6t...</td>
                                <td><span class="alert-level high"></span> 高危</td>
                                <td><span class="status green">已处置</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>2023-06-12 14:56</td>
                                <td>济南市政务服务中心</td>
                                <td>账号交易</td>
                                <td>济南市政务服务平台管理员账号出售</td>
                                <td>onion://5gt7y...</td>
                                <td><span class="alert-level medium"></span> 中危</td>
                                <td><span class="status green">已处置</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="pagination">
                    <div class="page-item">«</div>
                    <div class="page-item active">1</div>
                    <div class="page-item">2</div>
                    <div class="page-item">3</div>
                    <div class="page-item">»</div>
                </div>
            </div>
        </div>

        <div class="tab-content" id="internet-exposure">
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="输入单位名称、IP地址或域名搜索">
                <button class="search-btn">搜索</button>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-title">互联网资产总数</div>
                    <div class="stat-value blue">1,256</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">数据暴露总量</div>
                    <div class="stat-value red">186</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">敏感信息暴露</div>
                    <div class="stat-value orange">57</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">已处置暴露信息</div>
                    <div class="stat-value green">124</div>
                </div>
            </div>

            <div class="chart-container">
                <div class="chart">
                    <div class="chart-title">互联网暴露资产分布</div>
                    <div class="chart-placeholder">
                        <svg width="100%" height="100%" viewBox="0 0 400 250">
                            <!-- 柱状图模拟 -->
                            <rect x="50" y="50" width="40" height="150" fill="#1890ff" />
                            <rect x="100" y="80" width="40" height="120" fill="#1890ff" />
                            <rect x="150" y="100" width="40" height="100" fill="#1890ff" />
                            <rect x="200" y="70" width="40" height="130" fill="#1890ff" />
                            <rect x="250" y="110" width="40" height="90" fill="#1890ff" />
                            <rect x="300" y="130" width="40" height="70" fill="#1890ff" />
                            
                            <!-- 坐标轴 -->
                            <line x1="30" y1="200" x2="350" y2="200" stroke="#ddd" />
                            <line x1="30" y1="50" x2="30" y2="200" stroke="#ddd" />
                            
                            <!-- 标签 -->
                            <text x="70" y="220" font-size="12" fill="#666" text-anchor="middle">Web服务</text>
                            <text x="120" y="220" font-size="12" fill="#666" text-anchor="middle">数据库</text>
                            <text x="170" y="220" font-size="12" fill="#666" text-anchor="middle">API</text>
                            <text x="220" y="220" font-size="12" fill="#666" text-anchor="middle">文件服务</text>
                            <text x="270" y="220" font-size="12" fill="#666" text-anchor="middle">邮件服务</text>
                            <text x="320" y="220" font-size="12" fill="#666" text-anchor="middle">其他</text>
                        </svg>
                    </div>
                </div>
                <div class="chart">
                    <div class="chart-title">漏洞风险等级分布</div>
                    <div class="chart-placeholder">
                        <svg width="100%" height="100%" viewBox="0 0 400 250">
                            <!-- 饼图模拟 -->
                            <circle cx="150" cy="125" r="80" fill="#f5f5f5" stroke="#ddd" />
                            <path d="M 150 125 L 150 45 A 80 80 0 0 1 210 80 Z" fill="#f5222d" />
                            <path d="M 150 125 L 210 80 A 80 80 0 0 1 230 170 Z" fill="#faad14" />
                            <path d="M 150 125 L 230 170 A 80 80 0 0 1 70 170 Z" fill="#52c41a" />
                            <path d="M 150 125 L 70 170 A 80 80 0 0 1 150 45 Z" fill="#d9d9d9" />
                            
                            <!-- 图例 -->
                            <rect x="260" y="80" width="15" height="15" fill="#f5222d" />
                            <text x="285" y="93" font-size="12" fill="#666">高危 (15%)</text>
                            
                            <rect x="260" y="110" width="15" height="15" fill="#faad14" />
                            <text x="285" y="123" font-size="12" fill="#666">中危 (25%)</text>
                            
                            <rect x="260" y="140" width="15" height="15" fill="#52c41a" />
                            <text x="285" y="153" font-size="12" fill="#666">低危 (40%)</text>
                            
                            <rect x="260" y="170" width="15" height="15" fill="#d9d9d9" />
                            <text x="285" y="183" font-size="12" fill="#666">无风险 (20%)</text>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="table-container">
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>暴露位置</th>
                                <th>暴露内容</th>
                                <th>所属单位</th>
                                <th>暴露平台</th>
                                <th>数据量</th>
                                <th>风险等级</th>
                                <th>发现时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>百度网盘共享链接</td>
                                <td>济南市政务服务中心内部文档</td>
                                <td>济南市政务服务中心</td>
                                <td>百度网盘</td>
                                <td>2.3GB</td>
                                <td><span class="alert-level high"></span> 高危</td>
                                <td>2023-06-15 10:30</td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>GitHub公开仓库</td>
                                <td>济南市公共资源交易平台源码</td>
                                <td>济南市公共资源交易中心</td>
                                <td>GitHub</td>
                                <td>156MB</td>
                                <td><span class="alert-level medium"></span> 中危</td>
                                <td>2023-06-15 09:45</td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>CSDN博客文章</td>
                                <td>济南市大数据局API接口文档</td>
                                <td>济南市大数据局</td>
                                <td>CSDN</td>
                                <td>5篇</td>
                                <td><span class="alert-level high"></span> 高危</td>
                                <td>2023-06-14 16:20</td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>阿里云OSS公开链接</td>
                                <td>济南市卫健委医疗数据备份</td>
                                <td>济南市卫健委</td>
                                <td>阿里云OSS</td>
                                <td>1.7GB</td>
                                <td><span class="alert-level low"></span> 低危</td>
                                <td>2023-06-14 14:15</td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>腾讯文档共享链接</td>
                                <td>济南市人社局员工信息表</td>
                                <td>济南市人社局</td>
                                <td>腾讯文档</td>
                                <td>4份</td>
                                <td><span class="alert-level medium"></span> 中危</td>
                                <td>2023-06-13 11:30</td>
                                <td><a href="#">详情</a></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="pagination">
                    <div class="page-item">«</div>
                    <div class="page-item active">1</div>
                    <div class="page-item">2</div>
                    <div class="page-item">3</div>
                    <div class="page-item">»</div>
                </div>
            </div>
        </div>

        <div class="tab-content" id="cross-border-analysis">
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="输入单位名称、IP地址或目标国家/地区搜索">
                <button class="search-btn">搜索</button>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-title">今日异常跨境数据流量</div>
                    <div class="stat-value red">15.6 GB</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">本周异常跨境数据流量</div>
                    <div class="stat-value orange">78.3 GB</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">异常目标国家/地区</div>
                    <div class="stat-value blue">12</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">已处置异常事件</div>
                    <div class="stat-value green">23</div>
                </div>
            </div>

            <div class="map-container">
                <div class="map-overlay"></div>
                <!-- 模拟数据点 -->
                <div class="data-point" style="top: 40%; left: 20%;" title="济南市 -> 美国 (5.2GB)"></div>
                <div class="data-point medium" style="top: 35%; left: 30%;" title="济南市 -> 英国 (2.8GB)"></div>
                <div class="data-point" style="top: 45%; left: 40%;" title="济南市 -> 俄罗斯 (4.1GB)"></div>
                <div class="data-point medium" style="top: 50%; left: 60%;" title="济南市 -> 新加坡 (3.5GB)"></div>
                <div class="data-point low" style="top: 55%; left: 70%;" title="济南市 -> 澳大利亚 (1.2GB)"></div>
                
                <!-- 模拟数据流向 -->
                <div class="data-flow" style="top: 40%; left: 20%; width: 100px; transform: rotate(30deg);"></div>
                <div class="data-flow" style="top: 35%; left: 30%; width: 80px; transform: rotate(-15deg);"></div>
                <div class="data-flow" style="top: 45%; left: 40%; width: 120px; transform: rotate(10deg);"></div>
                <div class="data-flow" style="top: 50%; left: 60%; width: 90px; transform: rotate(-5deg);"></div>
                <div class="data-flow" style="top: 55%; left: 70%; width: 70px; transform: rotate(20deg);"></div>
            </div>

            <div class="chart-container">
                <div class="chart">
                    <div class="chart-title">异常跨境数据流量趋势（近7天）</div>
                    <div class="chart-placeholder">
                        <svg width="100%" height="100%" viewBox="0 0 500 250">
                            <!-- 坐标轴 -->
                            <line x1="50" y1="200" x2="450" y2="200" stroke="#ddd" stroke-width="1" />
                            <line x1="50" y1="50" x2="50" y2="200" stroke="#ddd" stroke-width="1" />
                            
                            <!-- 折线图 -->
                            <polyline points="50,150 110,160 170,130 230,170 290,100 350,80 410,120" 
                                    fill="none" stroke="#f5222d" stroke-width="2" />
                            
                            <!-- 数据点 -->
                            <circle cx="50" cy="150" r="3" fill="#f5222d" />
                            <circle cx="110" cy="160" r="3" fill="#f5222d" />
                            <circle cx="170" cy="130" r="3" fill="#f5222d" />
                            <circle cx="230" cy="170" r="3" fill="#f5222d" />
                            <circle cx="290" cy="100" r="3" fill="#f5222d" />
                            <circle cx="350" cy="80" r="3" fill="#f5222d" />
                            <circle cx="410" cy="120" r="3" fill="#f5222d" />
                            
                            <!-- 坐标轴标签 -->
                            <text x="50" y="220" font-size="10" text-anchor="middle" fill="#999">6/9</text>
                            <text x="110" y="220" font-size="10" text-anchor="middle" fill="#999">6/10</text>
                            <text x="170" y="220" font-size="10" text-anchor="middle" fill="#999">6/11</text>
                            <text x="230" y="220" font-size="10" text-anchor="middle" fill="#999">6/12</text>
                            <text x="290" y="220" font-size="10" text-anchor="middle" fill="#999">6/13</text>
                            <text x="350" y="220" font-size="10" text-anchor="middle" fill="#999">6/14</text>
                            <text x="410" y="220" font-size="10" text-anchor="middle" fill="#999">6/15</text>
                            
                            <text x="40" y="200" font-size="10" text-anchor="end" fill="#999">0</text>
                            <text x="40" y="150" font-size="10" text-anchor="end" fill="#999">10GB</text>
                            <text x="40" y="100" font-size="10" text-anchor="end" fill="#999">20GB</text>
                            <text x="40" y="50" font-size="10" text-anchor="end" fill="#999">30GB</text>
                        </svg>
                    </div>
                </div>
                <div class="chart">
                    <div class="chart-title">异常跨境数据目标国家/地区分布</div>
                    <div class="chart-placeholder">
                        <svg width="100%" height="100%" viewBox="0 0 400 250">
                            <!-- 柱状图模拟 -->
                            <rect x="50" y="80" width="30" height="120" fill="#f5222d" />
                            <rect x="90" y="110" width="30" height="90" fill="#f5222d" />
                            <rect x="130" y="130" width="30" height="70" fill="#faad14" />
                            <rect x="170" y="140" width="30" height="60" fill="#faad14" />
                            <rect x="210" y="150" width="30" height="50" fill="#1890ff" />
                            <rect x="250" y="160" width="30" height="40" fill="#1890ff" />
                            <rect x="290" y="170" width="30" height="30" fill="#52c41a" />
                            <rect x="330" y="180" width="30" height="20" fill="#52c41a" />
                            
                            <!-- 坐标轴 -->
                            <line x1="30" y1="200" x2="370" y2="200" stroke="#ddd" />
                            <line x1="30" y1="80" x2="30" y2="200" stroke="#ddd" />
                            
                            <!-- 标签 -->
                            <text x="65" y="220" font-size="10" fill="#666" text-anchor="middle">美国</text>
                            <text x="105" y="220" font-size="10" fill="#666" text-anchor="middle">俄罗斯</text>
                            <text x="145" y="220" font-size="10" fill="#666" text-anchor="middle">新加坡</text>
                            <text x="185" y="220" font-size="10" fill="#666" text-anchor="middle">英国</text>
                            <text x="225" y="220" font-size="10" fill="#666" text-anchor="middle">德国</text>
                            <text x="265" y="220" font-size="10" fill="#666" text-anchor="middle">日本</text>
                            <text x="305" y="220" font-size="10" fill="#666" text-anchor="middle">澳大利亚</text>
                            <text x="345" y="220" font-size="10" fill="#666" text-anchor="middle">其他</text>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="table-container">
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>发生时间</th>
                                <th>源IP</th>
                                <th>目标国家/地区</th>
                                <th>目标IP</th>
                                <th>数据量</th>
                                <th>所属单位</th>
                                <th>处置状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2023-06-15 09:23</td>
                                <td>172.16.xx.xx</td>
                                <td>美国</td>
                                <td>104.xx.xx.xx</td>
                                <td>2.3 GB</td>
                                <td>济南市大数据局</td>
                                <td><span class="status red">未处置</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>2023-06-15 08:12</td>
                                <td>172.16.xx.xx</td>
                                <td>俄罗斯</td>
                                <td>95.xx.xx.xx</td>
                                <td>1.8 GB</td>
                                <td>济南市政务服务中心</td>
                                <td><span class="status orange">处置中</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>2023-06-14 23:45</td>
                                <td>172.16.xx.xx</td>
                                <td>新加坡</td>
                                <td>128.xx.xx.xx</td>
                                <td>3.5 GB</td>
                                <td>济南市人社局</td>
                                <td><span class="status green">已处置</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>2023-06-14 16:34</td>
                                <td>172.16.xx.xx</td>
                                <td>英国</td>
                                <td>82.xx.xx.xx</td>
                                <td>1.2 GB</td>
                                <td>济南市公共资源交易中心</td>
                                <td><span class="status green">已处置</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>2023-06-13 14:22</td>
                                <td>172.16.xx.xx</td>
                                <td>澳大利亚</td>
                                <td>203.xx.xx.xx</td>
                                <td>0.8 GB</td>
                                <td>济南市卫健委</td>
                                <td><span class="status green">已处置</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="pagination">
                    <div class="page-item">«</div>
                    <div class="page-item active">1</div>
                    <div class="page-item">2</div>
                    <div class="page-item">3</div>
                    <div class="page-item">»</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简单的标签页切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');
            const submenuItems = document.querySelectorAll('.submenu-item');
            
            tabs.forEach((tab, index) => {
                tab.addEventListener('click', () => {
                    // 移除所有活动状态
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    // 设置当前标签为活动状态
                    tab.classList.add('active');
                    tabContents[index].classList.add('active');
                    
                    // 同步侧边栏菜单
                    submenuItems.forEach((item, i) => {
                        item.classList.remove('active');
                        if (i === index) {
                            item.classList.add('active');
                        }
                    });
                });
            });
            
            // 侧边栏子菜单点击
            submenuItems.forEach((item, index) => {
                item.addEventListener('click', () => {
                    // 移除所有活动状态
                    submenuItems.forEach(i => i.classList.remove('active'));
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    // 设置当前菜单和对应标签为活动状态
                    item.classList.add('active');
                    tabs[index].classList.add('active');
                    tabContents[index].classList.add('active');
                });
            });
        });
    </script>
</body>
</html>